<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>File Upload Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .upload-area {
            border: 2px dashed #4285f4;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            background-color: white;
            cursor: pointer;
            margin: 20px 0;
        }
        .upload-area:hover {
            background-color: #e3f2fd;
        }
        .upload-area.dragover {
            background-color: #e3f2fd;
            border-color: #1976d2;
        }
        .file-info {
            margin: 20px 0;
            padding: 10px;
            background-color: white;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        .console-log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>File Upload Test</h1>
    
    <div class="upload-area" id="uploadArea">
        <h3>📁 Click here or drag & drop a PDF file</h3>
        <p>This is a simple test to check if file upload works</p>
        <input type="file" id="fileInput" accept=".pdf" style="display: none;">
    </div>
    
    <div class="file-info" id="fileInfo" style="display: none;">
        <h4>File Information:</h4>
        <div id="fileDetails"></div>
    </div>
    
    <div class="console-log" id="consoleLog">
        <strong>Console Log:</strong><br>
    </div>

    <script>
        const consoleLog = document.getElementById('consoleLog');
        
        function log(message) {
            console.log(message);
            consoleLog.innerHTML += new Date().toLocaleTimeString() + ': ' + message + '\n';
            consoleLog.scrollTop = consoleLog.scrollHeight;
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            log('Page loaded successfully');
            
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');
            const fileInfo = document.getElementById('fileInfo');
            const fileDetails = document.getElementById('fileDetails');
            
            log('Elements found: uploadArea=' + !!uploadArea + ', fileInput=' + !!fileInput);
            
            if (uploadArea && fileInput) {
                // Click to upload
                uploadArea.addEventListener('click', () => {
                    log('Upload area clicked');
                    fileInput.click();
                });
                
                // Drag and drop
                uploadArea.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    uploadArea.classList.add('dragover');
                    log('Drag over detected');
                });
                
                uploadArea.addEventListener('dragleave', () => {
                    uploadArea.classList.remove('dragover');
                    log('Drag leave detected');
                });
                
                uploadArea.addEventListener('drop', (e) => {
                    e.preventDefault();
                    uploadArea.classList.remove('dragover');
                    const files = e.dataTransfer.files;
                    log('Files dropped: ' + files.length);
                    if (files.length > 0) {
                        handleFile(files[0]);
                    }
                });
                
                // File input change
                fileInput.addEventListener('change', (e) => {
                    log('File input changed');
                    if (e.target.files.length > 0) {
                        handleFile(e.target.files[0]);
                    }
                });
                
                log('All event listeners added successfully');
            } else {
                log('ERROR: Could not find required elements');
            }
        });
        
        function handleFile(file) {
            log('File selected: ' + file.name);
            log('File type: ' + file.type);
            log('File size: ' + file.size + ' bytes');
            
            // Show file info
            document.getElementById('fileInfo').style.display = 'block';
            document.getElementById('fileDetails').innerHTML = `
                <strong>Name:</strong> ${file.name}<br>
                <strong>Type:</strong> ${file.type}<br>
                <strong>Size:</strong> ${(file.size / 1024 / 1024).toFixed(2)} MB<br>
                <strong>Last Modified:</strong> ${new Date(file.lastModified).toLocaleString()}
            `;
            
            if (file.type === 'application/pdf') {
                log('✅ Valid PDF file detected');
                uploadArea.innerHTML = '<h3>✅ PDF Uploaded Successfully!</h3><p>' + file.name + '</p>';
                uploadArea.style.backgroundColor = '#d4edda';
                uploadArea.style.borderColor = '#28a745';
            } else {
                log('❌ Invalid file type. Please select a PDF file.');
                alert('Please select a PDF file.');
            }
        }
    </script>
</body>
</html>
