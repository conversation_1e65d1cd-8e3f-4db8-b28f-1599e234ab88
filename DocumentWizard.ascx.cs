using System;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace YourNamespace
{
    public partial class DocumentWizard : UserControl
    {
        #region Properties

        /// <summary>
        /// Gets the uploaded file name
        /// </summary>
        public string UploadedFileName
        {
            get { return hdnUploadedFileName.Value; }
        }

        /// <summary>
        /// Gets the current step
        /// </summary>
        public int CurrentStep
        {
            get 
            { 
                int step;
                return int.TryParse(hdnCurrentStep.Value, out step) ? step : 1;
            }
        }

        /// <summary>
        /// Gets the document name
        /// </summary>
        public string DocumentName
        {
            get { return txtName.Text.Trim(); }
            set { txtName.Text = value; }
        }

        /// <summary>
        /// Gets the selected document type
        /// </summary>
        public string DocumentType
        {
            get
            {
                if (rbDocType1.Checked) return "MTR";
                if (rbDocType3.Checked) return "WPS";
                if (rbDocType4.Checked) return "PQR";
                return "MTR"; // Default
            }
        }

        /// <summary>
        /// Gets the document type value
        /// </summary>
        public int DocumentTypeValue
        {
            get
            {
                if (rbDocType1.Checked) return 1;
                if (rbDocType3.Checked) return 3;
                if (rbDocType4.Checked) return 4;
                return 1; // Default
            }
        }

        /// <summary>
        /// Gets the description
        /// </summary>
        public string Description
        {
            get { return txtDescription.Text.Trim(); }
            set { txtDescription.Text = value; }
        }

        /// <summary>
        /// Gets the AI response
        /// </summary>
        public string AIResponse
        {
            get { return txtAIResponse.Text.Trim(); }
            set { txtAIResponse.Text = value; }
        }

        /// <summary>
        /// Gets the final notes
        /// </summary>
        public string FinalNotes
        {
            get { return txtFinalNotes.Text.Trim(); }
            set { txtFinalNotes.Text = value; }
        }

        /// <summary>
        /// Gets the selected heat numbers
        /// </summary>
        public string[] SelectedHeatNumbers
        {
            get
            {
                if (string.IsNullOrEmpty(hdnSelectedHeatNumbers.Value))
                    return new string[0];
                return hdnSelectedHeatNumbers.Value.Split(',');
            }
        }

        #endregion

        #region Events

        /// <summary>
        /// Event fired when AI validation is completed
        /// </summary>
        public event EventHandler<AIValidationEventArgs> AIValidationCompleted;

        /// <summary>
        /// Event fired when response is saved
        /// </summary>
        public event EventHandler<ResponseSavedEventArgs> ResponseSaved;

        /// <summary>
        /// Event fired when document processing is completed
        /// </summary>
        public event EventHandler<DocumentProcessedEventArgs> DocumentProcessed;

        #endregion

        #region Page Events

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                InitializeControl();
            }
        }

        #endregion

        #region Private Methods

        private void InitializeControl()
        {
            // Set default values
            hdnCurrentStep.Value = "1";
            rbDocType1.Checked = true; // Default to MTR
            
            // Register client script for better integration
            RegisterClientScript();
        }

        private void RegisterClientScript()
        {
            string script = @"
                // Additional client-side functionality can be added here
                function validateStep1() {
                    var fileName = document.getElementById('" + hdnUploadedFileName.ClientID + @"').value;
                    var docName = document.getElementById('" + txtName.ClientID + @"').value;
                    
                    if (!fileName) {
                        alert('Please upload a PDF file.');
                        return false;
                    }
                    
                    if (!docName.trim()) {
                        alert('Please enter a document name.');
                        return false;
                    }
                    
                    return true;
                }
            ";

            ClientScript.RegisterStartupScript(this.GetType(), "DocumentWizardScript", script, true);
        }

        #endregion

        #region Button Event Handlers

        protected void btnSaveResponse_Click(object sender, EventArgs e)
        {
            try
            {
                // Validate the response
                if (string.IsNullOrWhiteSpace(txtAIResponse.Text))
                {
                    // Show error message
                    ClientScript.RegisterStartupScript(this.GetType(), "Error", 
                        "alert('Please enter an AI response.');", true);
                    return;
                }

                // Fire the event
                var args = new ResponseSavedEventArgs
                {
                    DocumentName = this.DocumentName,
                    DocumentType = this.DocumentType,
                    Description = this.Description,
                    AIResponse = this.AIResponse,
                    SelectedHeatNumbers = this.SelectedHeatNumbers
                };

                ResponseSaved?.Invoke(this, args);

                // Move to step 4
                ClientScript.RegisterStartupScript(this.GetType(), "MoveToStep4", 
                    "switchStep(4);", true);
            }
            catch (Exception ex)
            {
                // Log error and show message
                ClientScript.RegisterStartupScript(this.GetType(), "Error", 
                    $"alert('Error saving response: {ex.Message}');", true);
            }
        }

        protected void btnProcessDocument_Click(object sender, EventArgs e)
        {
            try
            {
                // Validate all required fields
                if (!ValidateAllSteps())
                {
                    return;
                }

                // Fire the event
                var args = new DocumentProcessedEventArgs
                {
                    DocumentName = this.DocumentName,
                    DocumentType = this.DocumentType,
                    DocumentTypeValue = this.DocumentTypeValue,
                    Description = this.Description,
                    AIResponse = this.AIResponse,
                    FinalNotes = this.FinalNotes,
                    SelectedHeatNumbers = this.SelectedHeatNumbers,
                    UploadedFileName = this.UploadedFileName
                };

                DocumentProcessed?.Invoke(this, args);

                // Show success message
                ClientScript.RegisterStartupScript(this.GetType(), "Success", 
                    "alert('Document processed successfully!');", true);
            }
            catch (Exception ex)
            {
                // Log error and show message
                ClientScript.RegisterStartupScript(this.GetType(), "Error", 
                    $"alert('Error processing document: {ex.Message}');", true);
            }
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Validates all steps
        /// </summary>
        /// <returns>True if all steps are valid</returns>
        public bool ValidateAllSteps()
        {
            if (string.IsNullOrWhiteSpace(this.UploadedFileName))
            {
                ClientScript.RegisterStartupScript(this.GetType(), "Error", 
                    "alert('Please upload a PDF file.'); switchStep(1);", true);
                return false;
            }

            if (string.IsNullOrWhiteSpace(this.DocumentName))
            {
                ClientScript.RegisterStartupScript(this.GetType(), "Error", 
                    "alert('Please enter a document name.'); switchStep(1);", true);
                return false;
            }

            if (string.IsNullOrWhiteSpace(this.AIResponse))
            {
                ClientScript.RegisterStartupScript(this.GetType(), "Error", 
                    "alert('Please complete the AI response step.'); switchStep(3);", true);
                return false;
            }

            return true;
        }

        /// <summary>
        /// Resets the control to initial state
        /// </summary>
        public void Reset()
        {
            txtName.Text = string.Empty;
            txtDescription.Text = string.Empty;
            txtAIResponse.Text = string.Empty;
            txtFinalNotes.Text = string.Empty;
            hdnUploadedFileName.Value = string.Empty;
            hdnCurrentStep.Value = "1";
            hdnSelectedHeatNumbers.Value = string.Empty;
            rbDocType1.Checked = true;
            rbDocType3.Checked = false;
            rbDocType4.Checked = false;

            // Reset to step 1
            ClientScript.RegisterStartupScript(this.GetType(), "Reset", 
                "switchStep(1);", true);
        }

        /// <summary>
        /// Sets the AI response and moves to step 3
        /// </summary>
        /// <param name="response">The AI response</param>
        public void SetAIResponse(string response)
        {
            txtAIResponse.Text = response;
            ClientScript.RegisterStartupScript(this.GetType(), "SetAIResponse", 
                "switchStep(3);", true);
        }

        #endregion
    }

    #region Event Args Classes

    public class AIValidationEventArgs : EventArgs
    {
        public string DocumentName { get; set; }
        public string DocumentType { get; set; }
        public string Description { get; set; }
        public string UploadedFileName { get; set; }
    }

    public class ResponseSavedEventArgs : EventArgs
    {
        public string DocumentName { get; set; }
        public string DocumentType { get; set; }
        public string Description { get; set; }
        public string AIResponse { get; set; }
        public string[] SelectedHeatNumbers { get; set; }
    }

    public class DocumentProcessedEventArgs : EventArgs
    {
        public string DocumentName { get; set; }
        public string DocumentType { get; set; }
        public int DocumentTypeValue { get; set; }
        public string Description { get; set; }
        public string AIResponse { get; set; }
        public string FinalNotes { get; set; }
        public string[] SelectedHeatNumbers { get; set; }
        public string UploadedFileName { get; set; }
    }

    #endregion
}
