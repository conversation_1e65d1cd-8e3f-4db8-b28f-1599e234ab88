<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <system.web>
    <compilation debug="true" targetFramework="4.8" />
    <httpRuntime targetFramework="4.8" maxRequestLength="51200" executionTimeout="300" />
    
    <!-- Enable session state -->
    <sessionState mode="InProc" timeout="30" />
    
    <!-- Custom errors -->
    <customErrors mode="Off" />
    
    <!-- Pages configuration -->
    <pages controlRenderingCompatibilityVersion="4.0" clientIDMode="AutoID" />
    
    <!-- Trust level -->
    <trust level="Full" />
  </system.web>
  
  <system.webServer>
    <!-- Default document -->
    <defaultDocument>
      <files>
        <clear />
        <add value="TestPage.aspx" />
      </files>
    </defaultDocument>
    
    <!-- Static content -->
    <staticContent>
      <mimeMap fileExtension=".pdf" mimeType="application/pdf" />
    </staticContent>
    
    <!-- Security -->
    <security>
      <requestFiltering>
        <requestLimits maxAllowedContentLength="52428800" />
      </requestFiltering>
    </security>
  </system.webServer>
  
  <appSettings>
    <!-- Application settings -->
    <add key="ApplicationName" value="Document Wizard Test" />
    <add key="MaxFileSize" value="10485760" />
  </appSettings>
</configuration>
