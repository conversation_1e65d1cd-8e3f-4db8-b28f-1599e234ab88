<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document Upload Wizard - Standalone</title>
    
    <!-- Required Dependencies -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.14.305/pdf.min.js"></script>
    <script>
        // Fallback if PDF.js doesn't load
        if (typeof pdfjsLib === 'undefined') {
            console.error('PDF.js failed to load from CDN');
            document.write('<script src="https://unpkg.com/pdfjs-dist@2.14.305/build/pdf.min.js"><\/script>');
        }
    </script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet" />
    
    <style>
        /* Reset and Base Styles */
        * { box-sizing: border-box; }
        
        .wizard-container {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 100%;
            max-width: 1400px;
            height: 90vh;
            margin: 20px auto;
            display: flex;
            flex-direction: column;
        }

        .wizard-header {
            background-color: #4285f4;
            color: white;
            padding: 15px 20px;
            font-size: 18px;
            font-weight: 600;
        }

        .wizard-body {
            padding: 20px;
            flex: 1;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        /* Step Wizard Styles */
        .wizard-steps {
            display: flex;
            gap: 5px;
            margin-bottom: 30px;
            justify-content: flex-start;
            align-items: center;
        }

        .wizard-step {
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 18px 40px 18px 25px;
            min-width: 180px;
            height: 80px;
            text-align: center;
            background: #d4d4d4;
            color: #888888;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 1;
            clip-path: polygon(0 0, calc(100% - 20px) 0, 100% 50%, calc(100% - 20px) 100%, 0 100%, 20px 50%);
        }

        .wizard-step:first-child {
            clip-path: polygon(0 0, calc(100% - 20px) 0, 100% 50%, calc(100% - 20px) 100%, 0 100%);
            border-radius: 8px 0 0 8px;
        }

        .wizard-step:last-child {
            clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%, 20px 50%);
            border-radius: 0 8px 8px 0;
        }

        .wizard-step.active {
            background: #c8e6ff;
            color: #1976d2;
            font-weight: 600;
            z-index: 3;
        }

        .wizard-step:hover:not(.active) {
            background: #c0c0c0;
            z-index: 2;
        }

        .wizard-step .step-number {
            font-weight: 600;
            margin-bottom: 4px;
        }

        .wizard-step .step-title {
            font-size: 12px;
            font-weight: 400;
        }

        .wizard-step.active .step-title {
            color: #d32f2f;
            font-weight: 500;
        }

        /* Content Area */
        .step-content {
            flex: 1;
            display: none;
        }

        .step-content.active {
            display: flex;
            flex-direction: column;
        }

        .content-row {
            display: flex;
            gap: 20px;
            flex: 1;
            min-height: 0;
        }

        .content-left {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-height: 0;
        }

        .content-right {
            width: 350px;
            flex-shrink: 0;
            display: flex;
            flex-direction: column;
        }

        /* Upload Area */
        .upload-area {
            border: 2px dashed #4285f4;
            border-radius: 8px;
            padding: 40px 20px;
            text-align: center;
            background-color: #f8f9fa;
            margin-bottom: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .upload-area:hover, .upload-area.dragover {
            background-color: #e3f2fd;
            border-color: #1976d2;
        }

        .upload-icon {
            font-size: 48px;
            color: #4285f4;
            margin-bottom: 15px;
        }

        .upload-text {
            color: #6c757d;
            font-size: 16px;
            margin-bottom: 5px;
        }

        .upload-subtext {
            color: #9e9e9e;
            font-size: 14px;
        }

        /* PDF Viewer */
        .pdf-viewer-container {
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background-color: #f8f9fa;
            min-height: 400px;
        }

        .pdf-canvas-container {
            flex: 1;
            overflow: auto;
            width: 100%;
            height: 100%;
            padding: 10px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            display: none;
        }

        .pdf-canvas-container.active {
            display: block;
        }

        .pdf-canvas-container canvas {
            display: block;
            margin: 0 auto;
            border: 1px solid #ddd;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            background-color: white;
        }

        /* Form Styles */
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 8px;
            display: block;
        }

        .required {
            color: #dc3545;
        }

        .form-control {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }

        .form-control:focus {
            border-color: #4285f4;
            outline: none;
            box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
        }

        /* Radio Buttons */
        .radio-group {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }

        .radio-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .radio-item input[type="radio"] {
            appearance: none;
            width: 18px;
            height: 18px;
            border: 2px solid #ddd;
            border-radius: 50%;
            background-color: white;
            cursor: pointer;
            position: relative;
            transition: all 0.2s ease;
        }

        .radio-item input[type="radio"]:checked {
            border-color: #4285f4;
            background-color: #4285f4;
        }

        .radio-item input[type="radio"]:checked::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background-color: white;
        }

        .radio-item label {
            cursor: pointer;
            font-weight: 500;
            color: #333;
        }

        /* Button */
        .btn-primary {
            background-color: #4285f4;
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            transition: background-color 0.2s;
        }

        .btn-primary:hover {
            background-color: #3367d6;
        }

        .btn-primary:disabled {
            background-color: #9e9e9e;
            cursor: not-allowed;
        }

        /* PDF Controls */
        .pdf-controls {
            display: none;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 15px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 6px;
        }

        .pdf-controls.active {
            display: flex;
        }

        .pdf-btn {
            background-color: #e9ecef;
            border: 1px solid #ced4da;
            padding: 8px 16px;
            font-size: 14px;
            color: #495057;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .pdf-btn:hover {
            background-color: #dee2e6;
        }

        .pdf-page-info {
            font-size: 14px;
            color: #495057;
            font-weight: 500;
            padding: 8px 16px;
        }
    </style>
</head>
<body>
    <div class="wizard-container">
        <div class="wizard-header">
            Add New Document
        </div>
        
        <div class="wizard-body">
            <!-- Step Navigation -->
            <div class="wizard-steps">
                <div class="wizard-step active" data-step="1">
                    <div class="step-number">Step 1</div>
                    <div class="step-title">Add Document</div>
                </div>
                <div class="wizard-step" data-step="2">
                    <div class="step-number">Step 2</div>
                    <div class="step-title">AI Response Preview</div>
                </div>
                <div class="wizard-step" data-step="3">
                    <div class="step-number">Step 3</div>
                    <div class="step-title">Edit AI Response</div>
                </div>
                <div class="wizard-step" data-step="4">
                    <div class="step-number">Step 4</div>
                    <div class="step-title">Process Document</div>
                </div>
            </div>

            <!-- Step 1 Content -->
            <div class="step-content active" id="step1Content">
                <div class="content-row">
                    <!-- PDF Viewer -->
                    <div class="content-left">
                        <div class="pdf-viewer-container" id="pdfViewerContainer">
                            <div id="pdfPreviewArea">
                                <div class="upload-icon">
                                    <i class="fa fa-plus"></i>
                                </div>
                                <div class="upload-text">Upload a PDF to begin</div>
                            </div>
                            <div class="pdf-canvas-container" id="pdfCanvasContainer">
                                <canvas id="pdfCanvas"></canvas>
                            </div>
                        </div>
                        
                        <!-- PDF Controls -->
                        <div class="pdf-controls" id="pdfControls">
                            <button id="zoomOut" class="pdf-btn">Zoom Out</button>
                            <span id="zoomLevel" class="pdf-page-info">120%</span>
                            <button id="zoomIn" class="pdf-btn">Zoom In</button>
                            <span id="pageCount" class="pdf-page-info">Page 1 of 1</span>
                            <button id="prevPage" class="pdf-btn">Prev</button>
                            <button id="nextPage" class="pdf-btn">Next</button>
                        </div>
                    </div>

                    <!-- Upload Form -->
                    <div class="content-right">
                        <!-- Upload Area -->
                        <div class="upload-area" id="uploadArea">
                            <div class="upload-icon">
                                <i class="fa fa-upload"></i>
                            </div>
                            <div class="upload-text">Upload a file or drag and drop</div>
                            <div class="upload-subtext">(PDF up to 10MB)</div>
                            <input type="file" id="fileInput" accept=".pdf" style="display: none;">
                        </div>

                        <!-- Form Fields -->
                        <div class="form-group">
                            <label class="form-label">Name <span class="required">*</span>:</label>
                            <input id="txtName" type="text" placeholder="Document Name" class="form-control" />
                        </div>

                        <div class="form-group">
                            <label class="form-label">Document Type <span class="required">*</span>:</label>
                            <div class="radio-group">
                                <div class="radio-item">
                                    <input type="radio" value="1" name="rbDoc" id="rbDocType1" checked>
                                    <label for="rbDocType1">MTR</label>
                                </div>
                                <div class="radio-item">
                                    <input type="radio" value="3" name="rbDoc" id="rbDocType3">
                                    <label for="rbDocType3">WPS</label>
                                </div>
                                <div class="radio-item">
                                    <input type="radio" value="4" name="rbDoc" id="rbDocType4">
                                    <label for="rbDocType4">PQR</label>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Description:</label>
                            <textarea id="txtDescription" class="form-control" placeholder="Description" rows="4"></textarea>
                        </div>

                        <button type="button" class="btn-primary" id="btnTryAI" disabled>Run MTR AI Validation</button>
                    </div>
                </div>
            </div>

            <!-- Step 2 Content -->
            <div class="step-content" id="step2Content">
                <div class="content-row">
                    <div class="content-left">
                        <div class="pdf-viewer-container">
                            <div class="pdf-canvas-container active">
                                <canvas id="pdfCanvasStep2"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="content-right">
                        <h6>Select Heat Numbers</h6>
                        <p style="color: #6c757d;">The AI found heat numbers. Select which to include in the review.</p>
                        <div id="heatNumberList" style="margin-bottom: 20px;"></div>
                        <button class="btn-primary" id="btnContinueReview">Continue to Review</button>
                    </div>
                </div>
            </div>

            <!-- Step 3 Content -->
            <div class="step-content" id="step3Content">
                <h5>Step 3: Edit AI Response</h5>
                <p>This is the content for Step 3.</p>
            </div>

            <!-- Step 4 Content -->
            <div class="step-content" id="step4Content">
                <h5>Step 4: Process Document</h5>
                <p>This is the content for Step 4.</p>
            </div>
        </div>
    </div>

    <script>
        // Configure PDF.js worker
        pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.14.305/pdf.worker.min.js';

        // Global variables
        let pdfDoc = null;
        let pdfDocData = null;
        let pageNum = 1;
        let pageCount = 0;
        let scale = 1.2;
        let uploadedFilename = null;

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Standalone wizard initialized');
            initializeEventListeners();
        });

        function initializeEventListeners() {
            // File upload functionality
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');

            console.log('Initializing event listeners...');
            console.log('Upload area found:', uploadArea);
            console.log('File input found:', fileInput);

            if (uploadArea && fileInput) {
                console.log('Adding event listeners to upload elements');

                uploadArea.addEventListener('click', () => {
                    console.log('Upload area clicked - triggering file input');
                    fileInput.click();
                });

                uploadArea.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    uploadArea.classList.add('dragover');
                });

                uploadArea.addEventListener('dragleave', () => {
                    uploadArea.classList.remove('dragover');
                });

                uploadArea.addEventListener('drop', (e) => {
                    e.preventDefault();
                    uploadArea.classList.remove('dragover');
                    const files = e.dataTransfer.files;
                    if (files.length > 0) {
                        handleFileUpload(files[0]);
                    }
                });

                fileInput.addEventListener('change', (e) => {
                    console.log('File input changed, files:', e.target.files);
                    if (e.target.files.length > 0) {
                        console.log('File selected:', e.target.files[0].name);
                        handleFileUpload(e.target.files[0]);
                    } else {
                        console.log('No files selected');
                    }
                });
            }

            // Wizard step navigation
            document.querySelectorAll('.wizard-step').forEach(step => {
                step.addEventListener('click', () => {
                    const stepNum = step.getAttribute('data-step');
                    console.log('Step clicked:', stepNum);
                    switchStep(parseInt(stepNum));
                });
            });

            // AI validation button
            const btnTryAI = document.getElementById('btnTryAI');
            if (btnTryAI) {
                btnTryAI.addEventListener('click', () => {
                    if (!uploadedFilename) {
                        alert('Please upload a PDF file first.');
                        return;
                    }
                    alert("Running AI validation on: " + uploadedFilename);
                    switchStep(2);
                });
            }

            // Continue to review button
            const btnContinueReview = document.getElementById('btnContinueReview');
            if (btnContinueReview) {
                btnContinueReview.addEventListener('click', () => {
                    switchStep(3);
                });
            }

            // PDF controls
            const prevPage = document.getElementById('prevPage');
            const nextPage = document.getElementById('nextPage');
            const zoomIn = document.getElementById('zoomIn');
            const zoomOut = document.getElementById('zoomOut');

            if (prevPage) {
                prevPage.addEventListener('click', () => {
                    if (pageNum <= 1) return;
                    pageNum--;
                    renderPage(pageNum);
                });
            }

            if (nextPage) {
                nextPage.addEventListener('click', () => {
                    if (pageNum >= pdfDoc.numPages) return;
                    pageNum++;
                    renderPage(pageNum);
                });
            }

            if (zoomIn) {
                zoomIn.addEventListener('click', () => {
                    scale += 0.2;
                    updateZoomLevel();
                    renderPage(pageNum);
                });
            }

            if (zoomOut) {
                zoomOut.addEventListener('click', () => {
                    if (scale > 0.4) {
                        scale -= 0.2;
                        updateZoomLevel();
                        renderPage(pageNum);
                    }
                });
            }
        }

        function handleFileUpload(file) {
            console.log('File upload started:', file ? file.name : 'No file');
            console.log('File type:', file ? file.type : 'No file');
            console.log('File size:', file ? file.size : 'No file');

            if (file && file.type === 'application/pdf') {
                console.log('Valid PDF file detected');
                uploadedFilename = file.name;
                const btnTryAI = document.getElementById('btnTryAI');
                if (btnTryAI) {
                    btnTryAI.disabled = false;
                }

                const reader = new FileReader();
                reader.onload = function(e) {
                    console.log('File read successfully, size:', e.target.result.byteLength);
                    pdfDocData = new Uint8Array(e.target.result);
                    console.log('Attempting to load PDF with PDF.js...');
                    pdfjsLib.getDocument({ data: pdfDocData }).promise.then(function(pdf) {
                        console.log('PDF loaded successfully, pages:', pdf.numPages);
                        pdfDoc = pdf;
                        pageCount = pdf.numPages;
                        pageNum = 1;
                        showPDFViewer();
                        renderPage(pageNum);
                    }).catch(function(error) {
                        console.error('Error loading PDF:', error);
                        alert('Error loading PDF file: ' + error.message);
                    });
                };
                reader.onerror = function(error) {
                    console.error('Error reading file:', error);
                    alert('Error reading file.');
                };
                reader.readAsArrayBuffer(file);
            } else {
                alert('Please select a valid PDF file.');
            }
        }

        function showPDFViewer() {
            const pdfPreviewArea = document.getElementById('pdfPreviewArea');
            const pdfCanvasContainer = document.getElementById('pdfCanvasContainer');
            const pdfControls = document.getElementById('pdfControls');

            if (pdfPreviewArea) pdfPreviewArea.style.display = 'none';
            if (pdfCanvasContainer) pdfCanvasContainer.classList.add('active');
            if (pdfControls) pdfControls.classList.add('active');

            updateZoomLevel();
        }

        function renderPage(num, canvasId = 'pdfCanvas') {
            if (!pdfDoc) return;

            const canvas = document.getElementById(canvasId);
            if (!canvas) return;

            const ctx = canvas.getContext('2d');

            pdfDoc.getPage(num).then(function(page) {
                const viewport = page.getViewport({ scale: scale });
                canvas.height = viewport.height;
                canvas.width = viewport.width;

                ctx.clearRect(0, 0, canvas.width, canvas.height);
                page.render({ canvasContext: ctx, viewport });

                if (canvasId === 'pdfCanvas') {
                    const pageCount = document.getElementById('pageCount');
                    if (pageCount) {
                        pageCount.textContent = `Page ${pageNum} of ${pdfDoc.numPages}`;
                    }
                }
            });
        }

        function updateZoomLevel() {
            const zoomElement = document.getElementById('zoomLevel');
            if (zoomElement) {
                zoomElement.textContent = Math.round(scale * 100) + '%';
            }
        }

        function switchStep(step) {
            console.log('Switching to step:', step);

            // Update wizard steps
            document.querySelectorAll('.wizard-step').forEach(s => s.classList.remove('active'));
            const targetStep = document.querySelector(`[data-step="${step}"]`);
            if (targetStep) {
                targetStep.classList.add('active');
            }

            // Update content
            document.querySelectorAll('.step-content').forEach(s => s.classList.remove('active'));
            const targetContent = document.getElementById(`step${step}Content`);
            if (targetContent) {
                targetContent.classList.add('active');
            }

            // Special handling for step 2
            if (step === 2) {
                renderStep2PDF();
                setTimeout(() => {
                    populateHeatNumbers(['HN001', 'HN002', 'HN003']);
                }, 500);
            }
        }

        function renderStep2PDF() {
            if (!pdfDocData) return;

            const canvasStep2 = document.getElementById('pdfCanvasStep2');
            if (!canvasStep2) return;

            const ctx2 = canvasStep2.getContext('2d');
            pdfjsLib.getDocument({ data: pdfDocData }).promise.then(function(pdf) {
                pdf.getPage(1).then(function(page) {
                    const viewport = page.getViewport({ scale: 1.2 });
                    canvasStep2.width = viewport.width;
                    canvasStep2.height = viewport.height;
                    page.render({ canvasContext: ctx2, viewport });
                });
            });
        }

        function populateHeatNumbers(heatNumbers) {
            const container = document.getElementById('heatNumberList');
            if (!container) return;

            container.innerHTML = '';

            if (!heatNumbers || heatNumbers.length === 0) {
                container.innerHTML = '<p style="color: #6c757d;">No heat numbers found.</p>';
                return;
            }

            heatNumbers.forEach((heat, index) => {
                const id = `heat_${index}`;
                const div = document.createElement('div');
                div.style.marginBottom = '10px';
                div.innerHTML = `
                    <input type="checkbox" value="${heat}" id="${id}" checked style="margin-right: 8px;">
                    <label for="${id}" style="cursor: pointer;">${heat}</label>
                `;
                container.appendChild(div);
            });
        }
    </script>
</body>
</html>
