<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <title>Step Wizard + PDF Preview</title>

    <!-- Bootstrap & jQuery -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

    <!-- Dropzone & PDF.js -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.3/min/dropzone.min.css" rel="stylesheet" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.3/min/dropzone.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.blockUI/2.70/jquery.blockUI.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.14.305/pdf.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet" />


    <style>
    /* Modal Styles */
    .modal-backdrop {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1040;
    }

    .modal-dialog-custom {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 90%;
        max-width: 1200px;
        max-height: 90vh;
        background: white;
        border-radius: 8px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        z-index: 1050;
        overflow: hidden;
    }

    .modal-header-custom {
        background-color: #4285f4;
        color: white;
        padding: 15px 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .modal-header-custom h5 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
    }

    .modal-close {
        background: none;
        border: none;
        color: white;
        font-size: 24px;
        cursor: pointer;
        padding: 0;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .modal-body-custom {
        padding: 20px;
        max-height: calc(90vh - 80px);
        overflow-y: auto;
    }

    /* Step Wizard Styles */
    .wizard-steps {
        display: flex;
        gap: 5px;
        margin-bottom: 30px;
        justify-content: center;
        align-items: center;
    }

    .wizard-step {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 15px 25px;
        min-width: 160px;
        height: 70px;
        text-align: center;
        background: #e9ecef;
        color: #6c757d;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.3s ease;
        border-radius: 12px;
        margin-right: 0;
        z-index: 1;
    }

    .wizard-step::after {
        content: '';
        position: absolute;
        right: -15px;
        top: 50%;
        transform: translateY(-50%);
        width: 0;
        height: 0;
        border-left: 15px solid #e9ecef;
        border-top: 15px solid transparent;
        border-bottom: 15px solid transparent;
        z-index: 2;
        transition: border-left-color 0.3s ease;
    }

    .wizard-step:last-child::after {
        display: none;
    }

    .wizard-step.active {
        background: #b3d9ff;
        color: #1565c0;
        font-weight: 600;
        z-index: 3;
    }

    .wizard-step.active::after {
        border-left-color: #b3d9ff;
    }

    .wizard-step:hover:not(.active) {
        background: #dee2e6;
        z-index: 2;
    }

    .wizard-step:hover:not(.active)::after {
        border-left-color: #dee2e6;
    }

    .wizard-step .step-number {
        font-weight: bold;
        font-size: 14px;
        margin-bottom: 3px;
        line-height: 1;
    }

    .wizard-step .step-title {
        font-size: 12px;
        line-height: 1.2;
        font-weight: 500;
    }

    .wizard-step.active .step-number {
        font-weight: 700;
    }

    .wizard-step.active .step-title {
        font-weight: 600;
    }

    /* PDF Viewer Styles */
    .pdf-viewer-container {
        border: 2px dashed #dee2e6;
        border-radius: 8px;
        min-height: 400px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background-color: #f8f9fa;
        position: relative;
    }

    .pdf-canvas-container {
        max-height: 400px;
        overflow: auto;
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .pdf-canvas-container canvas {
        max-width: 100%;
        height: auto;
    }

    .pdf-controls {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 10px;
        margin-top: 15px;
        padding: 10px;
        background-color: #f8f9fa;
        border-radius: 6px;
    }

    .pdf-btn {
        background-color: #e9ecef;
        border: 1px solid #ced4da;
        padding: 8px 16px;
        font-size: 14px;
        color: #495057;
        border-radius: 4px;
        cursor: pointer;
        transition: background-color 0.2s;
    }

    .pdf-btn:hover {
        background-color: #dee2e6;
    }

    .pdf-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }

    .pdf-page-info {
        font-size: 14px;
        color: #495057;
        font-weight: 500;
        padding: 8px 16px;
    }

    /* Upload Area Styles */
    .upload-area {
        border: 2px dashed #4285f4;
        border-radius: 8px;
        padding: 40px 20px;
        text-align: center;
        background-color: #f8f9fa;
        margin-bottom: 20px;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .upload-area:hover {
        background-color: #e3f2fd;
        border-color: #1976d2;
    }

    .upload-area.dragover {
        background-color: #e3f2fd;
        border-color: #1976d2;
    }

    .upload-icon {
        font-size: 48px;
        color: #4285f4;
        margin-bottom: 15px;
    }

    .upload-text {
        color: #6c757d;
        font-size: 16px;
        margin-bottom: 5px;
    }

    .upload-subtext {
        color: #9e9e9e;
        font-size: 14px;
    }

    /* Form Styles */
    .form-group {
        margin-bottom: 20px;
    }

    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 8px;
        display: block;
    }

    .required {
        color: #dc3545;
    }

    .form-control {
        width: 100%;
        padding: 10px 12px;
        border: 1px solid #ced4da;
        border-radius: 4px;
        font-size: 14px;
    }

    .form-control:focus {
        border-color: #4285f4;
        outline: none;
        box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
    }

    .radio-group {
        display: flex;
        gap: 20px;
        flex-wrap: wrap;
    }

    .radio-item {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .radio-item input[type="radio"] {
        margin: 0;
    }

    .btn-primary-custom {
        background-color: #4285f4;
        border: none;
        color: white;
        padding: 12px 24px;
        border-radius: 6px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        width: 100%;
        transition: background-color 0.2s;
    }

    .btn-primary-custom:hover {
        background-color: #3367d6;
    }

    .btn-primary-custom:disabled {
        background-color: #9e9e9e;
        cursor: not-allowed;
    }

    .d-none {
        display: none !important;
    }

    .row {
        display: flex;
        gap: 20px;
    }

    .col-left {
        flex: 1;
    }

    .col-right {
        width: 350px;
        flex-shrink: 0;
    }

    @media (max-width: 768px) {
        .row {
            flex-direction: column;
        }

        .col-right {
            width: 100%;
        }

        .wizard-steps {
            flex-wrap: wrap;
            gap: 10px;
        }

        .wizard-step {
            min-width: 140px;
            padding: 10px 15px;
        }
    }
</style>

</head>
<body>
    <!-- Button to open modal -->
    <div class="container mt-5">
        <button type="button" class="btn btn-primary" id="openModalBtn">Add New Document</button>
    </div>

    <!-- Modal Backdrop -->
    <div class="modal-backdrop d-none" id="modalBackdrop"></div>

    <!-- Modal Dialog -->
    <div class="modal-dialog-custom d-none" id="modalDialog">
        <!-- Modal Header -->
        <div class="modal-header-custom">
            <h5>Add New Document</h5>
            <button type="button" class="modal-close" id="closeModalBtn">&times;</button>
        </div>

        <!-- Modal Body -->
        <div class="modal-body-custom">
            <!-- Step Wizard -->
            <div class="wizard-steps">
                <div class="wizard-step active" data-step="1">
                    <div class="step-number">Step 1</div>
                    <div class="step-title">Add Document</div>
                </div>
                <div class="wizard-step" data-step="2">
                    <div class="step-number">Step 2</div>
                    <div class="step-title">AI Response Preview</div>
                </div>
                <div class="wizard-step" data-step="3">
                    <div class="step-number">Step 3</div>
                    <div class="step-title">Edit AI Response</div>
                </div>
                <div class="wizard-step" data-step="4">
                    <div class="step-number">Step 4</div>
                    <div class="step-title">Process Document</div>
                </div>
            </div>

            <!-- Step 1 Content -->
            <div class="step-content" id="step1Content">
                <div class="row">
                    <!-- PDF Viewer -->
                    <div class="col-left">
                        <div class="pdf-viewer-container" id="pdfViewerContainer">
                            <div id="pdfPreviewArea">
                                <div class="upload-icon">
                                    <i class="fa fa-plus"></i>
                                </div>
                                <div class="upload-text">Upload a PDF to begin</div>
                            </div>
                            <div class="pdf-canvas-container d-none" id="pdfCanvasContainer">
                                <canvas id="pdfCanvas"></canvas>
                            </div>
                        </div>

                        <!-- PDF Controls -->
                        <div class="pdf-controls d-none" id="pdfControls">
                            <button id="zoomOut" class="pdf-btn">Zoom Out</button>
                            <span id="pageCount" class="pdf-page-info">Page 1 of 1</span>
                            <button id="zoomIn" class="pdf-btn">Zoom In</button>
                            <button id="prevPage" class="pdf-btn">Prev</button>
                            <button id="nextPage" class="pdf-btn">Next</button>
                        </div>
                    </div>

                    <!-- Upload Form -->
                    <div class="col-right">
                        <!-- Upload Area -->
                        <div class="upload-area" id="uploadArea">
                            <div class="upload-icon">
                                <i class="fa fa-upload"></i>
                            </div>
                            <div class="upload-text">Upload a file or drag and drop</div>
                            <div class="upload-subtext">(PDF up to 10MB)</div>
                            <input type="file" id="fileInput" accept=".pdf" style="display: none;">
                        </div>

                        <!-- Form Fields -->
                        <div class="form-group">
                            <label class="form-label">Name <span class="required">*</span>:</label>
                            <input id="txtName" type="text" placeholder="Document Name" class="form-control" />
                        </div>

                        <div class="form-group">
                            <label class="form-label">Document Type <span class="required">*</span>:</label>
                            <div class="radio-group">
                                <div class="radio-item">
                                    <input type="radio" value="1" name="rbDoc" id="rbDocType1" checked>
                                    <label for="rbDocType1">MTR</label>
                                </div>
                                <div class="radio-item">
                                    <input type="radio" value="3" name="rbDoc" id="rbDocType3">
                                    <label for="rbDocType3">WPS</label>
                                </div>
                                <div class="radio-item">
                                    <input type="radio" value="4" name="rbDoc" id="rbDocType4">
                                    <label for="rbDocType4">PQR</label>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Description:</label>
                            <textarea id="txtDescription" class="form-control" placeholder="Description" rows="4"></textarea>
                        </div>

                        <button type="button" class="btn-primary-custom" id="btnTryAI" disabled>Run MTR AI Validation</button>
                    </div>
                </div>
            </div>

            <!-- Step 2 Content -->
            <div class="step-content d-none" id="step2Content">
                <div class="row">
                    <div class="col-left">
                        <div class="pdf-viewer-container">
                            <div class="pdf-canvas-container">
                                <canvas id="pdfCanvasStep2"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-right">
                        <h6>Select Heat Numbers</h6>
                        <p class="text-muted">The AI found heat numbers. Select which to include in the review.</p>
                        <div id="heatNumberList" class="mb-3"></div>
                        <button class="btn-primary-custom" id="btnContinueReview">Continue to Review</button>
                    </div>
                </div>
            </div>

            <!-- Step 3 Content -->
            <div class="step-content d-none" id="step3Content">
                <h5>Step 3: Edit AI Response</h5>
                <p>This is the content for Step 3.</p>
            </div>

            <!-- Step 4 Content -->
            <div class="step-content d-none" id="step4Content">
                <h5>Step 4: Process Document</h5>
                <p>This is the content for Step 4.</p>
            </div>
        </div>
    </div>

    <script>
        let pdfDoc = null;
        let pdfDocData = null;
        let pageNum = 1;
        let pageCount = 0;
        let scale = 1.2;
        let uploadedFilename = null;

        // Modal functions
        function openModal() {
            document.getElementById('modalBackdrop').classList.remove('d-none');
            document.getElementById('modalDialog').classList.remove('d-none');
        }

        function closeModal() {
            document.getElementById('modalBackdrop').classList.add('d-none');
            document.getElementById('modalDialog').classList.add('d-none');
        }

        // PDF rendering functions
        function renderPage(num, canvasId = 'pdfCanvas') {
            if (!pdfDoc) return;

            const canvas = document.getElementById(canvasId);
            const ctx = canvas.getContext('2d');

            pdfDoc.getPage(num).then(function (page) {
                const viewport = page.getViewport({ scale: scale });
                canvas.height = viewport.height;
                canvas.width = viewport.width;
                page.render({ canvasContext: ctx, viewport });

                if (canvasId === 'pdfCanvas') {
                    document.getElementById('pageCount').textContent = `Page ${pageNum} of ${pdfDoc.numPages}`;
                }
            });
        }

        function renderStep2PDF() {
            if (!pdfDocData) return;
            const canvasStep2 = document.getElementById('pdfCanvasStep2');
            const ctx2 = canvasStep2.getContext('2d');
            pdfjsLib.getDocument({ data: pdfDocData }).promise.then(function (pdf) {
                pdf.getPage(1).then(function (page) {
                    const viewport = page.getViewport({ scale: 1.2 });
                    canvasStep2.width = viewport.width;
                    canvasStep2.height = viewport.height;
                    page.render({ canvasContext: ctx2, viewport });
                });
            });
        }

        function showPDFViewer() {
            document.getElementById('pdfPreviewArea').classList.add('d-none');
            document.getElementById('pdfCanvasContainer').classList.remove('d-none');
            document.getElementById('pdfControls').classList.remove('d-none');
        }

        function hidePDFViewer() {
            document.getElementById('pdfPreviewArea').classList.remove('d-none');
            document.getElementById('pdfCanvasContainer').classList.add('d-none');
            document.getElementById('pdfControls').classList.add('d-none');
        }

        function handleFileUpload(file) {
            if (file && file.type === 'application/pdf') {
                uploadedFilename = file.name;
                document.getElementById('btnTryAI').disabled = false;

                const reader = new FileReader();
                reader.onload = function (e) {
                    pdfDocData = new Uint8Array(e.target.result);
                    pdfjsLib.getDocument({ data: pdfDocData }).promise.then(function (pdf) {
                        pdfDoc = pdf;
                        pageCount = pdf.numPages;
                        pageNum = 1;
                        showPDFViewer();
                        renderPage(pageNum);
                    });
                };
                reader.readAsArrayBuffer(file);
            } else {
                alert('Please select a valid PDF file.');
            }
        }

        function populateHeatNumbers(heatNumbers) {
            const container = document.getElementById('heatNumberList');
            container.innerHTML = '';

            if (!heatNumbers || heatNumbers.length === 0) {
                container.innerHTML = '<p class="text-muted">No heat numbers found.</p>';
                return;
            }

            heatNumbers.forEach((heat, index) => {
                const id = `heat_${index}`;
                const div = document.createElement('div');
                div.className = 'form-check mb-2';
                div.innerHTML = `
                    <input class="form-check-input" type="checkbox" value="${heat}" id="${id}" checked>
                    <label class="form-check-label" for="${id}">${heat}</label>
                `;
                container.appendChild(div);
            });
        }

        function switchStep(step) {
            // Update wizard steps
            document.querySelectorAll('.wizard-step').forEach(s => s.classList.remove('active'));
            document.querySelector(`[data-step="${step}"]`).classList.add('active');

            // Update content
            document.querySelectorAll('.step-content').forEach(s => s.classList.add('d-none'));
            document.getElementById(`step${step}Content`).classList.remove('d-none');

            if (step === 2) {
                renderStep2PDF();
                // Simulate API call for heat numbers
                setTimeout(() => {
                    populateHeatNumbers(['HN001', 'HN002', 'HN003']);
                }, 500);
            }
        }

        // Event listeners
        document.addEventListener('DOMContentLoaded', function() {
            // Modal controls
            document.getElementById('openModalBtn').addEventListener('click', openModal);
            document.getElementById('closeModalBtn').addEventListener('click', closeModal);
            document.getElementById('modalBackdrop').addEventListener('click', closeModal);

            // File upload
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');

            uploadArea.addEventListener('click', () => fileInput.click());
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });
            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('dragover');
            });
            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    handleFileUpload(files[0]);
                }
            });

            fileInput.addEventListener('change', (e) => {
                if (e.target.files.length > 0) {
                    handleFileUpload(e.target.files[0]);
                }
            });

            // PDF controls
            document.getElementById('prevPage').addEventListener('click', () => {
                if (pageNum <= 1) return;
                pageNum--;
                renderPage(pageNum);
            });

            document.getElementById('nextPage').addEventListener('click', () => {
                if (pageNum >= pdfDoc.numPages) return;
                pageNum++;
                renderPage(pageNum);
            });

            document.getElementById('zoomIn').addEventListener('click', () => {
                scale += 0.2;
                renderPage(pageNum);
            });

            document.getElementById('zoomOut').addEventListener('click', () => {
                if (scale > 0.4) {
                    scale -= 0.2;
                    renderPage(pageNum);
                }
            });

            // Wizard navigation
            document.querySelectorAll('.wizard-step').forEach(step => {
                step.addEventListener('click', () => {
                    const stepNum = step.getAttribute('data-step');
                    switchStep(parseInt(stepNum));
                });
            });

            // AI validation button
            document.getElementById('btnTryAI').addEventListener('click', () => {
                if (!uploadedFilename) {
                    alert('No uploaded file available for validation.');
                    return;
                }
                alert("Running AI validation on: " + uploadedFilename);
                // Switch to step 2 after validation
                switchStep(2);
            });

            // Continue to review button
            document.getElementById('btnContinueReview').addEventListener('click', () => {
                switchStep(3);
            });
        });
    </script>
</body>
</html>
