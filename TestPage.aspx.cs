using System;
using System.Text;
using System.Web.UI;

namespace YourNamespace
{
    public partial class TestPage : Page
    {
        private StringBuilder eventLog = new StringBuilder();

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                RefreshStatus();
                LogEvent("Page loaded successfully");
            }
            else
            {
                // Restore event log from ViewState
                if (ViewState["EventLog"] != null)
                {
                    eventLog.Append(ViewState["EventLog"].ToString());
                }
            }
        }

        protected void Page_PreRender(object sender, EventArgs e)
        {
            // Save event log to ViewState
            ViewState["EventLog"] = eventLog.ToString();
            lblEventLog.Text = eventLog.ToString();
        }

        #region Button Event Handlers

        protected void btnRefreshStatus_Click(object sender, EventArgs e)
        {
            RefreshStatus();
            LogEvent("Status refreshed manually");
        }

        protected void btnResetControl_Click(object sender, EventArgs e)
        {
            try
            {
                DocumentWizard1.Reset();
                RefreshStatus();
                LogEvent("Control reset successfully");
            }
            catch (Exception ex)
            {
                LogEvent($"Error resetting control: {ex.Message}");
            }
        }

        protected void btnSetSampleData_Click(object sender, EventArgs e)
        {
            try
            {
                DocumentWizard1.DocumentName = "Sample MTR Document";
                DocumentWizard1.Description = "This is a sample document for testing purposes.";
                DocumentWizard1.AIResponse = "Sample AI response: The document appears to be a valid MTR with all required fields completed.";
                DocumentWizard1.FinalNotes = "Sample final notes: Document reviewed and approved.";
                
                RefreshStatus();
                LogEvent("Sample data set successfully");
            }
            catch (Exception ex)
            {
                LogEvent($"Error setting sample data: {ex.Message}");
            }
        }

        #endregion

        #region Document Wizard Event Handlers

        protected void DocumentWizard1_DocumentProcessed(object sender, DocumentProcessedEventArgs e)
        {
            try
            {
                LogEvent("🎉 Document Processed Event Fired!");
                LogEvent($"   Document Name: {e.DocumentName}");
                LogEvent($"   Document Type: {e.DocumentType} (Value: {e.DocumentTypeValue})");
                LogEvent($"   Description: {e.Description}");
                LogEvent($"   Uploaded File: {e.UploadedFileName}");
                LogEvent($"   AI Response Length: {e.AIResponse?.Length ?? 0} characters");
                LogEvent($"   Final Notes Length: {e.FinalNotes?.Length ?? 0} characters");
                LogEvent($"   Selected Heat Numbers: {string.Join(", ", e.SelectedHeatNumbers ?? new string[0])}");

                // Here you would typically save to database, call web services, etc.
                // For demo purposes, we'll just log the event
                
                RefreshStatus();
                
                // Show success message
                string script = "alert('Document processed successfully!\\n\\n" +
                               $"Document: {e.DocumentName}\\n" +
                               $"Type: {e.DocumentType}\\n" +
                               $"Heat Numbers: {string.Join(", ", e.SelectedHeatNumbers ?? new string[0])}');";
                ClientScript.RegisterStartupScript(this.GetType(), "ProcessSuccess", script, true);
            }
            catch (Exception ex)
            {
                LogEvent($"❌ Error in DocumentProcessed event: {ex.Message}");
            }
        }

        protected void DocumentWizard1_ResponseSaved(object sender, ResponseSavedEventArgs e)
        {
            try
            {
                LogEvent("💾 Response Saved Event Fired!");
                LogEvent($"   Document Name: {e.DocumentName}");
                LogEvent($"   Document Type: {e.DocumentType}");
                LogEvent($"   AI Response Length: {e.AIResponse?.Length ?? 0} characters");
                LogEvent($"   Selected Heat Numbers: {string.Join(", ", e.SelectedHeatNumbers ?? new string[0])}");

                // Here you would typically save the AI response to database
                // For demo purposes, we'll just log the event
                
                RefreshStatus();
                
                // Show success message
                string script = "alert('AI Response saved successfully!');";
                ClientScript.RegisterStartupScript(this.GetType(), "SaveSuccess", script, true);
            }
            catch (Exception ex)
            {
                LogEvent($"❌ Error in ResponseSaved event: {ex.Message}");
            }
        }

        #endregion

        #region Private Methods

        private void RefreshStatus()
        {
            try
            {
                lblCurrentStep.Text = DocumentWizard1.CurrentStep.ToString();
                lblUploadedFile.Text = string.IsNullOrEmpty(DocumentWizard1.UploadedFileName) 
                    ? "None" : DocumentWizard1.UploadedFileName;
                lblDocumentName.Text = string.IsNullOrEmpty(DocumentWizard1.DocumentName) 
                    ? "Not set" : DocumentWizard1.DocumentName;
                lblDocumentType.Text = DocumentWizard1.DocumentType;
            }
            catch (Exception ex)
            {
                LogEvent($"Error refreshing status: {ex.Message}");
            }
        }

        private void LogEvent(string message)
        {
            string timestamp = DateTime.Now.ToString("HH:mm:ss");
            eventLog.AppendLine($"[{timestamp}] {message}");
        }

        #endregion
    }
}
