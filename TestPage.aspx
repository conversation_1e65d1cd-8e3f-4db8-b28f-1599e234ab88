<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="TestPage.aspx.cs" Inherits="YourNamespace.TestPage" %>
<%@ Register Src="~/DocumentWizard.ascx" TagName="DocumentWizard" TagPrefix="uc" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title>Document Wizard Test Page</title>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
            border-radius: 8px;
        }
        
        .info-panel {
            background-color: white;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .info-panel h3 {
            margin-top: 0;
            color: #2c3e50;
        }
        
        .status-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .status-item {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            border-left: 4px solid #4285f4;
        }
        
        .status-item strong {
            color: #2c3e50;
        }
        
        .control-buttons {
            margin-top: 15px;
        }
        
        .btn {
            background-color: #4285f4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            font-size: 14px;
        }
        
        .btn:hover {
            background-color: #3367d6;
        }
        
        .btn-secondary {
            background-color: #6c757d;
        }
        
        .btn-secondary:hover {
            background-color: #545b62;
        }
        
        .wizard-container {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .alert {
            padding: 12px 20px;
            margin-bottom: 20px;
            border-radius: 4px;
            border-left: 4px solid;
        }
        
        .alert-info {
            background-color: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        
        .alert-success {
            background-color: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        
        .alert-warning {
            background-color: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <div class="container">
            <div class="header">
                <h1>📄 Document Wizard Test Page</h1>
                <p>Testing the ASCX User Control</p>
            </div>
            
            <div class="alert alert-info">
                <strong>ℹ️ Info:</strong> This page demonstrates the DocumentWizard ASCX user control. 
                Upload a PDF file and test the wizard functionality.
            </div>
            
            <!-- Status Panel -->
            <div class="info-panel">
                <h3>📊 Control Status</h3>
                <div class="status-info">
                    <div class="status-item">
                        <strong>Current Step:</strong><br />
                        <asp:Label ID="lblCurrentStep" runat="server" Text="1" />
                    </div>
                    <div class="status-item">
                        <strong>Uploaded File:</strong><br />
                        <asp:Label ID="lblUploadedFile" runat="server" Text="None" />
                    </div>
                    <div class="status-item">
                        <strong>Document Name:</strong><br />
                        <asp:Label ID="lblDocumentName" runat="server" Text="Not set" />
                    </div>
                    <div class="status-item">
                        <strong>Document Type:</strong><br />
                        <asp:Label ID="lblDocumentType" runat="server" Text="Not selected" />
                    </div>
                </div>
                
                <div class="control-buttons">
                    <asp:Button ID="btnRefreshStatus" runat="server" Text="🔄 Refresh Status" 
                        CssClass="btn" OnClick="btnRefreshStatus_Click" />
                    <asp:Button ID="btnResetControl" runat="server" Text="🔄 Reset Control" 
                        CssClass="btn btn-secondary" OnClick="btnResetControl_Click" />
                    <asp:Button ID="btnSetSampleData" runat="server" Text="📝 Set Sample Data" 
                        CssClass="btn btn-secondary" OnClick="btnSetSampleData_Click" />
                </div>
            </div>
            
            <!-- Event Log Panel -->
            <div class="info-panel">
                <h3>📋 Event Log</h3>
                <asp:Label ID="lblEventLog" runat="server" Text="No events yet..." />
            </div>
            
            <!-- Document Wizard Control -->
            <div class="wizard-container">
                <uc:DocumentWizard ID="DocumentWizard1" runat="server" 
                    OnDocumentProcessed="DocumentWizard1_DocumentProcessed" 
                    OnResponseSaved="DocumentWizard1_ResponseSaved" />
            </div>
        </div>
    </form>
</body>
</html>
